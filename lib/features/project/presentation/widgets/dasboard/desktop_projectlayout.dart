import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_bottomsheet.dart';
import 'package:cp_associates/features/project/presentation/widgets/dasboard/sidedrawer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DesktopProjectLayout extends StatefulWidget {
  DesktopProjectLayout({
    super.key,
    required this.projectId,
    required this.child,
  });
  final String projectId;
  final Widget child;

  @override
  State<DesktopProjectLayout> createState() => _DesktopProjectLayoutState();
}

class _DesktopProjectLayoutState extends State<DesktopProjectLayout> {
  void initState() {
    super.initState();

    context.read<ProjectCubit>().fetchProjectDetail(widget.projectId);

    context.read<ProjectCubit>().checkUserAccess(
      context.read<ProjectCubit>().state.projectDetail,
      FBAuth.auth.currentUser?.uid ?? "",
      context,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProjectCubit, ProjectState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Scaffold(body: Center(child: CircularProgressIndicator()));
        }
        print("_____${state.projectDetail}");
        return Scaffold(
          backgroundColor: const Color.fromARGB(255, 252, 250, 250),
          // backgroundColor: Colors.grey.shade900,
          body: Row(
            // mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SideDrawer(projectId: widget.projectId),
              SizedBox(width: 60),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 150),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.borderGrey),
                    ),

                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                    child: widget.child,
                  ),
                ),
              ),
              SizedBox(width: 20),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 150, right: 100),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.borderGrey),
                      color: AppColors.containerGreyColor,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [ProjectDetailBottomsheet()],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
